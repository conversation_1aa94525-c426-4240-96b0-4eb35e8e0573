// 工作安排导入模态框组件
const { CurrencyUtils } = require('../../utils/currency-utils.js')

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示
    visible: {
      type: Boolean,
      value: false
    },
    // 目标日期（要导入到的日期）
    targetDate: {
      type: String,
      value: ''
    },
    // 当前工作ID
    workId: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 日历相关
    calendarYear: new Date().getFullYear(),
    calendarMonth: new Date().getMonth() + 1,
    calendarDays: [],
    hasAnyData: false,
    
    // 选择相关
    selectedDate: null,
    selectedDateData: null,
    selectedDateObject: null,
    
    // 模态框动画状态
    modalVisible: false,

    // 货币设置
    currencySymbol: '¥'
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 监听属性变化
     */
    _onPropertiesChange() {
      if (this.data.visible) {
        this._initializeModal()
      } else {
        this.setData({ modalVisible: false })
      }
    },

    /**
     * 初始化模态框
     */
    _initializeModal() {
      // 加载货币设置
      this._loadCurrencySettings()

      const now = new Date()
      this.setData({
        calendarYear: now.getFullYear(),
        calendarMonth: now.getMonth() + 1,
        selectedDate: null,
        selectedDateData: null,
        selectedDateObject: null
      }, () => {
        this.generateCalendar()
        // 延迟显示动画
        setTimeout(() => {
          this.setData({ modalVisible: true })
        }, 50)
      })
    },

    /**
     * 加载货币设置
     */
    _loadCurrencySettings() {
      try {
        CurrencyUtils.loadCurrencySettingsForContext(this, {
          includeSymbol: true
        })
      } catch (error) {
        console.error('加载货币设置失败:', error)
        this.setData({
          currencySymbol: '¥'
        })
      }
    },

    /**
     * 获取时间段服务实例
     */
    _getTimeSegmentService() {
      // 尝试从页面获取服务实例
      const pages = getCurrentPages()
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1]
        if (currentPage.timeSegmentService) {
          return currentPage.timeSegmentService
        }
      }

      // 如果页面没有，尝试从全局获取
      if (typeof getApp === 'function') {
        const app = getApp()
        if (app.globalData && app.globalData.timeSegmentService) {
          return app.globalData.timeSegmentService
        }
      }

      return null
    },

    /**
     * 生成日历数据
     */
    generateCalendar() {
      const timeSegmentService = this._getTimeSegmentService()
      if (!timeSegmentService) {
        console.warn('timeSegmentService not available')
        return
      }

      const { calendarYear, calendarMonth } = this.data
      
      // 获取当月第一天和最后一天
      const firstDay = new Date(calendarYear, calendarMonth - 1, 1)
      const lastDay = new Date(calendarYear, calendarMonth, 0)
      
      // 获取当月第一天是星期几（0-6，0是星期日）
      const firstDayOfWeek = firstDay.getDay()
      
      // 获取当月天数
      const daysInMonth = lastDay.getDate()
      
      // 生成日历数据
      const calendarDays = []
      let hasAnyData = false
      
      // 添加上个月的空白天数
      for (let i = 0; i < firstDayOfWeek; i++) {
        calendarDays.push(null)
      }
      
      // 添加当月的天数
      for (let day = 1; day <= daysInMonth; day++) {
        const date = new Date(calendarYear, calendarMonth - 1, day)
        const dayData = timeSegmentService.getDayData(date)
        const hasData = dayData.segments.length > 0
        const status = timeSegmentService.getDateStatus(date, this.properties.workId)
        const statusConfig = timeSegmentService.getDateStatusConfig(status)
        
        // 检查是否在任职日期范围内
        const isInEmploymentRange = this._isDateInEmploymentRange(date)
        
        if (hasData) {
          hasAnyData = true
        }
        
        calendarDays.push({
          day,
          date,
          hasData,
          isCurrentMonth: true,
          status,
          statusConfig,
          dailyIncome: dayData.dailyIncome || 0,
          segmentCount: dayData.segments.length,
          isInEmploymentRange
        })
      }
      
      this.setData({
        calendarDays,
        hasAnyData
      })
    },

    /**
     * 检查日期是否在任职范围内
     */
    _isDateInEmploymentRange(date) {
      // 通过全局方法检查日期是否在任职范围内
      if (typeof getApp === 'function') {
        const app = getApp()
        if (app.globalData && app.globalData.isDateInEmploymentRange) {
          return app.globalData.isDateInEmploymentRange(date)
        }
      }

      // 如果没有全局方法，默认返回true
      return true
    },

    /**
     * 上一年
     */
    onPreviousYear() {
      this.setData({
        calendarYear: this.data.calendarYear - 1
      })
      this.generateCalendar()
    },

    /**
     * 下一年
     */
    onNextYear() {
      this.setData({
        calendarYear: this.data.calendarYear + 1
      })
      this.generateCalendar()
    },

    /**
     * 上一月
     */
    onPreviousMonth() {
      let { calendarYear, calendarMonth } = this.data
      
      calendarMonth--
      if (calendarMonth < 1) {
        calendarMonth = 12
        calendarYear--
      }
      
      this.setData({
        calendarYear,
        calendarMonth
      })
      
      this.generateCalendar()
    },

    /**
     * 下一月
     */
    onNextMonth() {
      let { calendarYear, calendarMonth } = this.data
      
      calendarMonth++
      if (calendarMonth > 12) {
        calendarMonth = 1
        calendarYear++
      }
      
      this.setData({
        calendarYear,
        calendarMonth
      })
      
      this.generateCalendar()
    },

    /**
     * 日期点击
     */
    onDateTap(e) {
      const { index } = e.currentTarget.dataset
      const dayItem = this.data.calendarDays[index]
      
      if (!dayItem || !dayItem.isCurrentMonth || !dayItem.hasData) {
        if (dayItem && dayItem.isCurrentMonth && !dayItem.hasData) {
          wx.showToast({
            title: '该日期没有工作安排',
            icon: 'none'
          })
        }
        return
      }

      // 获取详细数据
      const timeSegmentService = this._getTimeSegmentService()
      if (!timeSegmentService) {
        console.warn('timeSegmentService not available for date selection')
        return
      }

      const dayData = timeSegmentService.getDayData(dayItem.date)
      const selectedDateData = {
        ...dayData,
        statusConfig: dayItem.statusConfig
      }
      
      this.setData({
        selectedDate: this._formatDate(dayItem.date),
        selectedDateData,
        selectedDateObject: dayItem.date
      })
    },

    /**
     * 确认导入
     */
    onConfirm() {
      if (!this.data.selectedDateObject) {
        wx.showToast({
          title: '请先选择要导入的日期',
          icon: 'none'
        })
        return
      }
      
      const sourceDate = this.data.selectedDateObject
      const targetDate = new Date(this.properties.targetDate)
      const scheduleData = this.data.selectedDateData
      
      this.triggerEvent('confirm', {
        sourceDate,
        targetDate,
        scheduleData
      })
      
      this.onClose()
    },

    /**
     * 取消
     */
    onCancel() {
      this.triggerEvent('cancel')
      this.onClose()
    },

    /**
     * 关闭模态框
     */
    onClose() {
      // 开始出场动画
      this.setData({ modalVisible: false })
      
      // 等待动画完成后触发关闭事件
      setTimeout(() => {
        this.triggerEvent('close')
      }, 300) // 与CSS动画时长一致
    },

    /**
     * 阻止事件冒泡
     */
    onStopPropagation() {
      // 阻止点击模态框内容时关闭模态框
    },

    /**
     * 格式化日期
     */
    _formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    }
  },

  /**
   * 组件生命周期
   */
  observers: {
    'visible': function(visible) {
      this._onPropertiesChange()
    }
  }
})
