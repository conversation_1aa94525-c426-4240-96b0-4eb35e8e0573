/**
 * 应用配置文件
 * 统一管理应用中的各种配置项，避免重复定义
 */

/**
 * 货币配置
 * 包含货币代码、显示标签、符号、单位等完整信息
 */
const CURRENCY_CONFIG = {
  CNY: {
    code: 'CNY',
    label: '人民币 (¥)',
    symbol: '¥',
    unit: '元',
    hourlyUnit: '元/小时',
    dailyUnit: '元/天',
    monthlyUnit: '元/月',
    name: '人民币'
  },
  USD: {
    code: 'USD',
    label: '美元 ($)',
    symbol: '$',
    unit: '美元',
    hourlyUnit: '美元/小时',
    dailyUnit: '美元/天',
    monthlyUnit: '美元/月',
    name: '美元'
  },
  EUR: {
    code: 'EUR',
    label: '欧元 (€)',
    symbol: '€',
    unit: '欧元',
    hourlyUnit: '欧元/小时',
    dailyUnit: '欧元/天',
    monthlyUnit: '欧元/月',
    name: '欧元'
  },
  JPY: {
    code: 'JPY',
    label: '日元 (¥)',
    symbol: '¥',
    unit: '日元',
    hourlyUnit: '日元/小时',
    dailyUnit: '日元/天',
    monthlyUnit: '日元/月',
    name: '日元'
  },
  GBP: {
    code: 'GBP',
    label: '英镑 (£)',
    symbol: '£',
    unit: '英镑',
    hourlyUnit: '英镑/小时',
    dailyUnit: '英镑/天',
    monthlyUnit: '英镑/月',
    name: '英镑'
  },
  KRW: {
    code: 'KRW',
    label: '韩元 (₩)',
    symbol: '₩',
    unit: '韩元',
    hourlyUnit: '韩元/小时',
    dailyUnit: '韩元/天',
    monthlyUnit: '韩元/月',
    name: '韩元'
  },
  HKD: {
    code: 'HKD',
    label: '港币 (HK$)',
    symbol: 'HK$',
    unit: '港币',
    hourlyUnit: '港币/小时',
    dailyUnit: '港币/天',
    monthlyUnit: '港币/月',
    name: '港币'
  },
  TWD: {
    code: 'TWD',
    label: '新台币 (NT$)',
    symbol: 'NT$',
    unit: '新台币',
    hourlyUnit: '新台币/小时',
    dailyUnit: '新台币/天',
    monthlyUnit: '新台币/月',
    name: '新台币'
  }
}

/**
 * 货币选项列表（用于UI选择器）
 * 从CURRENCY_CONFIG生成，确保数据一致性
 */
const CURRENCY_OPTIONS = Object.values(CURRENCY_CONFIG).map(config => ({
  value: config.code,
  label: config.label,
  symbol: config.symbol
}))

/**
 * 默认货币设置
 */
const DEFAULT_CURRENCY = {
  code: 'CNY',
  symbol: '¥'
}

/**
 * 配置工具类
 */
class AppConfig {
  /**
   * 获取货币配置
   * @param {string} currencyCode - 货币代码
   * @returns {Object} 货币配置对象
   */
  static getCurrencyConfig(currencyCode) {
    return CURRENCY_CONFIG[currencyCode] || CURRENCY_CONFIG[DEFAULT_CURRENCY.code]
  }
  
  /**
   * 获取所有货币配置
   * @returns {Object} 所有货币配置
   */
  static getAllCurrencyConfigs() {
    return CURRENCY_CONFIG
  }
  
  /**
   * 获取货币选项列表
   * @returns {Array} 货币选项数组
   */
  static getCurrencyOptions() {
    return CURRENCY_OPTIONS
  }
  
  /**
   * 获取默认货币设置
   * @returns {Object} 默认货币设置
   */
  static getDefaultCurrency() {
    return DEFAULT_CURRENCY
  }
  
  /**
   * 检查货币代码是否支持
   * @param {string} currencyCode - 货币代码
   * @returns {boolean} 是否支持
   */
  static isSupportedCurrency(currencyCode) {
    return currencyCode in CURRENCY_CONFIG
  }
}

module.exports = {
  // 配置常量
  CURRENCY_CONFIG,
  CURRENCY_OPTIONS,
  DEFAULT_CURRENCY,

  // 配置工具类
  AppConfig
}
