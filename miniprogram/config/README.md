# 应用配置文件说明

## 概述

`app-config.js` 是应用的统一配置文件，集中管理所有配置项，避免重复定义，提高维护性和扩展性。

## 配置结构

### 1. 货币配置 (CURRENCY_CONFIG)

统一管理所有货币相关配置，包括：

- **货币代码**: 如 CNY, USD, EUR 等
- **显示标签**: 如 "人民币 (¥)", "美元 ($)" 等
- **货币符号**: 如 ¥, $, € 等
- **货币单位**: 如 元, 美元, 欧元 等
- **时薪单位**: 如 元/小时, 美元/小时 等
- **日薪单位**: 如 元/天, 美元/天 等
- **月薪单位**: 如 元/月, 美元/月 等

```javascript
const config = AppConfig.getCurrencyConfig('USD')
// 返回: { code: 'USD', symbol: '$', unit: '美元', ... }
```

## 设计原则

配置文件遵循以下设计原则：

- **按需配置**: 只包含当前实际使用的配置项
- **避免过度设计**: 不预先添加未来可能用到的配置
- **保持简洁**: 配置结构清晰，易于理解和维护
- **支持扩展**: 架构设计支持未来添加新的配置类型

## 使用方法

### 1. 引入配置

```javascript
const { AppConfig } = require('../../config/app-config.js')
```

### 2. 获取货币配置

```javascript
// 获取货币选项列表（用于UI选择器）
const currencyOptions = AppConfig.getCurrencyOptions()

// 获取特定货币配置
const usdConfig = AppConfig.getCurrencyConfig('USD')

// 获取默认货币
const defaultCurrency = AppConfig.getDefaultCurrency()

// 检查货币是否支持
const isSupported = AppConfig.isSupportedCurrency('EUR')
```



## 配置优化成果

### 1. 消除重复定义

**之前**:
- `miniprogram/pages/profile/index.js` 中定义货币选项
- `miniprogram/utils/currency-utils.js` 中定义货币配置
- 两处定义容易不一致，维护困难

**现在**:
- 所有货币配置统一在 `app-config.js` 中定义
- 其他模块通过 `AppConfig` 获取配置
- 确保配置一致性

### 2. 提高维护性

- **单一数据源**: 所有配置集中管理
- **统一接口**: 通过 `AppConfig` 类提供统一的访问接口
- **易于修改**: 添加新货币或修改配置只需要在一个地方进行

### 3. 增强扩展性

- **模块化设计**: 配置结构清晰，易于扩展
- **按需扩展**: 根据实际需要添加新的配置类型
- **未来友好**: 可以轻松添加主题、语言、地区等新配置

### 4. 保持一致性

- **统一数据源**: 所有模块使用相同的配置
- **类型安全**: 通过工具类方法确保数据格式正确
- **默认值管理**: 统一的默认值和fallback机制

## 扩展指南

### 添加新货币

1. 在 `CURRENCY_CONFIG` 中添加新货币配置
2. 新货币会自动出现在 `CURRENCY_OPTIONS` 中
3. 所有使用货币配置的模块会自动支持新货币

```javascript
// 在 CURRENCY_CONFIG 中添加
CAD: {
  code: 'CAD',
  label: '加元 (C$)',
  symbol: 'C$',
  unit: '加元',
  hourlyUnit: '加元/小时',
  dailyUnit: '加元/天',
  monthlyUnit: '加元/月',
  name: '加元'
}
```

### 添加新配置类型

当需要添加新的配置类型时，可以按照以下步骤：

1. 在配置文件中定义新的配置常量
2. 在 `AppConfig` 类中添加对应的获取方法
3. 在需要的模块中使用新配置
4. 更新相关文档

```javascript
// 示例：添加主题配置
const THEME_CONFIG = {
  light: { name: '浅色主题', background: '#FFFFFF' },
  dark: { name: '深色主题', background: '#000000' }
}

// 在 AppConfig 类中添加方法
static getThemeConfig(themeName) {
  return THEME_CONFIG[themeName] || THEME_CONFIG.light
}
```

## 注意事项

1. **向后兼容**: 修改配置时要考虑现有代码的兼容性
2. **默认值**: 确保所有配置都有合理的默认值
3. **类型检查**: 在获取配置时进行必要的类型检查
4. **文档更新**: 添加新配置时要更新相关文档

## 相关文件

- `miniprogram/config/app-config.js` - 主配置文件
- `miniprogram/utils/currency-utils.js` - 货币工具类（使用统一配置）
- `miniprogram/pages/profile/index.js` - 个人设置页面（使用统一配置）
- `miniprogram/utils/formatters/data-formatter.js` - 数据格式化工具（使用统一配置）
